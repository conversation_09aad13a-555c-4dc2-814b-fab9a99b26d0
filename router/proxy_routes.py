"""
统一代理路由实现
整合格式检测、转换和转发功能的通用路由处理器
"""

import time
import json
from typing import Dict, Any, Optional
from fastapi import APIRouter, Request, Response, HTTPException
from fastapi.responses import StreamingResponse, JSONResponse

from service.format_detector import CachedFormatDetector
from service.litellm_converter import LiteLLMConverter
from service.proxy_forwarder import ProxyForwarder
from models.conversion_models import LLMFormat
from config.settings import settings
from log.logger import get_router_logger

logger = get_router_logger()

# 创建路由器
router = APIRouter()

# 初始化核心服务
format_detector = CachedFormatDetector()
litellm_converter = LiteLLMConverter()
proxy_forwarder = ProxyForwarder()

logger.info("Proxy router initialized with core services")


@router.api_route("/{path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH", "OPTIONS"], include_in_schema=False)
async def proxy_handler(request: Request, path: str) -> Response:
    """
    统一代理路由处理器
    处理所有类型的 LLM API 请求，支持格式检测、转换和转发
    
    Args:
        request: FastAPI 请求对象
        path: 请求路径
        
    Returns:
        代理响应
    """
    start_time = time.time()
    request_id = f"{int(time.time() * 1000)}-{hash(str(request.url)) % 10000}"
    
    try:
        logger.info(f"[{request_id}] Processing {request.method} {path}")
        
        # 1. 格式检测
        detection_result = await format_detector.detect_format(request)
        source_format = detection_result.detected_format
        confidence = detection_result.confidence
        
        logger.info(f"[{request_id}] Detected format: {source_format.value} (confidence: {confidence:.2f})")
        
        # 2. 请求数据提取
        request_data = await _extract_request_data(request)
        
        # 3. 格式转换
        if source_format != LLMFormat.UNKNOWN:
            logger.debug(f"[{request_id}] Converting from {source_format.value} to OpenAI format")
            conversion_result = await litellm_converter.convert_to_openai_format(
                request_data, source_format, path
            )

            if not conversion_result.success:
                logger.error(f"[{request_id}] Format conversion failed: {conversion_result.error_message}")
                return JSONResponse(
                    status_code=400,
                    content={
                        "error": "Format conversion failed",
                        "detail": conversion_result.error_message,
                        "source_format": source_format.value,
                        "request_id": request_id
                    }
                )

            converted_data = conversion_result.converted_data
            logger.debug(f"[{request_id}] Format conversion successful, converted data keys: {list(converted_data.keys()) if isinstance(converted_data, dict) else 'non-dict'}")
        else:
            # 未知格式，直接使用原始数据
            converted_data = request_data
            logger.warning(f"[{request_id}] Unknown format, using original data")
        
        # 4. 检查是否为流式请求
        is_stream = _is_stream_request(request, converted_data)

        # 5. 确定目标路径
        # 如果进行了格式转换，使用OpenAI格式的路径
        if source_format != LLMFormat.UNKNOWN and source_format != LLMFormat.OPENAI:
            target_path = "/v1/chat/completions"
            logger.debug(f"[{request_id}] Format converted from {source_format.value}, using OpenAI path: {target_path}")
        else:
            target_path = f"/{path}" if not path.startswith('/') else path
            logger.debug(f"[{request_id}] Using original path: {target_path}")

        # 6. 请求转发
        logger.debug(f"[{request_id}] Forwarding request: method={request.method}, path={target_path}, stream={is_stream}")
        response = await proxy_forwarder.forward_request(
            method=request.method,
            path=target_path,
            headers=dict(request.headers),
            data=converted_data,
            params=dict(request.query_params),
            stream=is_stream
        )
        
        # 7. 记录处理时间
        process_time = (time.time() - start_time) * 1000  # 转换为毫秒
        logger.info(f"[{request_id}] Request processed in {process_time:.1f}ms")

        # 8. 添加处理信息到响应头
        if hasattr(response, 'headers'):
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Process-Time"] = f"{process_time:.1f}ms"
            response.headers["X-Source-Format"] = source_format.value
            response.headers["X-Detection-Confidence"] = f"{confidence:.2f}"
        
        return response
        
    except Exception as e:
        process_time = (time.time() - start_time) * 1000
        logger.error(f"[{request_id}] Request processing failed after {process_time:.1f}ms: {str(e)}", exc_info=True)

        # 根据错误类型提供更具体的错误信息
        error_type = "proxy_error"
        error_message = "Request processing failed"

        if "Content-Length" in str(e):
            error_type = "content_length_error"
            error_message = "Content-Length mismatch error"
        elif "timeout" in str(e).lower():
            error_type = "timeout_error"
            error_message = "Request timeout"
        elif "connection" in str(e).lower():
            error_type = "connection_error"
            error_message = "Connection error"
        elif "conversion" in str(e).lower():
            error_type = "conversion_error"
            error_message = "Format conversion error"

        return JSONResponse(
            status_code=500,
            content={
                "error": {
                    "code": "http_error",
                    "message": f"Internal server error during {path.replace('/', '_').replace(':', '_')}"
                },
                "detail": str(e),
                "type": error_type,
                "request_id": request_id,
                "process_time_ms": process_time
            }
        )


async def _extract_request_data(request: Request) -> Optional[Dict[str, Any]]:
    """
    提取请求数据
    
    Args:
        request: FastAPI 请求对象
        
    Returns:
        请求数据字典，如果没有数据则返回 None
    """
    if request.method in ["POST", "PUT", "PATCH"]:
        try:
            body = await request.body()
            if body:
                content_type = request.headers.get("content-type", "")
                if "application/json" in content_type:
                    return json.loads(body.decode())
                elif "application/x-www-form-urlencoded" in content_type:
                    # 处理表单数据
                    form_data = await request.form()
                    return dict(form_data)
                else:
                    # 其他类型的数据，返回原始字节
                    return {"raw_data": body.decode("utf-8", errors="ignore")}
        except Exception as e:
            logger.warning(f"Failed to extract request data: {str(e)}")
    
    return None


def _is_stream_request(request: Request, data: Optional[Dict[str, Any]]) -> bool:
    """
    判断是否为流式请求
    
    Args:
        request: FastAPI 请求对象
        data: 请求数据
        
    Returns:
        是否为流式请求
    """
    # 检查查询参数
    if request.query_params.get("stream") == "true":
        return True
    
    # 检查请求体中的 stream 参数
    if data and isinstance(data, dict) and data.get("stream") is True:
        return True
    
    # 检查 Accept 头部
    accept_header = request.headers.get("accept", "")
    if "text/event-stream" in accept_header:
        return True
    
    return False


async def health_check():
    """健康检查端点"""
    try:
        # 检查各个服务的健康状态
        forwarder_health = await proxy_forwarder.health_check()
        detector_stats = format_detector.get_detection_stats()
        converter_formats = litellm_converter.get_supported_formats()
        
        return JSONResponse(
            status_code=200,
            content={
                "status": "healthy",
                "service": "LLM Format Proxy Router",
                "timestamp": time.time(),
                "components": {
                    "format_detector": {
                        "status": "healthy",
                        "supported_formats": detector_stats["supported_formats"],
                        "cache_enabled": settings.ENABLE_FORMAT_CACHE
                    },
                    "litellm_converter": {
                        "status": "healthy",
                        "supported_formats": converter_formats
                    },
                    "proxy_forwarder": forwarder_health
                }
            }
        )
    except Exception as e:
        logger.error(f"Health check failed: {str(e)}")
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": time.time()
            }
        )


async def get_stats():
    """获取代理服务统计信息"""
    try:
        return JSONResponse(
            status_code=200,
            content={
                "format_detector": {
                    "detection_stats": format_detector.get_detection_stats(),
                    "cache_stats": format_detector.get_cache_stats()
                },
                "litellm_converter": {
                    "supported_formats": litellm_converter.get_supported_formats()
                },
                "proxy_forwarder": proxy_forwarder.get_stats(),
                "settings": {
                    "target_base_url": settings.TARGET_BASE_URL,
                    "max_concurrent_requests": settings.MAX_CONCURRENT_REQUESTS,
                    "request_timeout": settings.REQUEST_TIMEOUT,
                    "format_detection_threshold": settings.FORMAT_DETECTION_CONFIDENCE_THRESHOLD
                }
            }
        )
    except Exception as e:
        logger.error(f"Stats retrieval failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Failed to retrieve stats",
                "detail": str(e)
            }
        )


async def debug_detect_format(request: Request):
    """调试端点：仅进行格式检测"""
    try:
        detection_result = await format_detector.detect_format(request)
        
        return JSONResponse(
            status_code=200,
            content={
                "detected_format": detection_result.detected_format.value,
                "confidence": detection_result.confidence,
                "detection_method": detection_result.detection_method,
                "metadata": detection_result.metadata
            }
        )
    except Exception as e:
        logger.error(f"Debug format detection failed: {str(e)}")
        return JSONResponse(
            status_code=500,
            content={
                "error": "Format detection failed",
                "detail": str(e)
            }
        )
