"""
代理转发服务实现
将转换后的请求发送到目标服务器，支持代理、超时、重试等功能
"""

import asyncio
import json
from typing import Dict, Any, Optional, AsyncGenerator, Union
from fastapi import Request, Response
from fastapi.responses import StreamingResponse, JSONResponse

from service.client.proxy_client import ProxyClient
from config.settings import settings
from log.logger import get_proxy_forwarder_logger

logger = get_proxy_forwarder_logger()


class ProxyForwarder:
    """代理转发服务类"""
    
    def __init__(self, target_base_url: Optional[str] = None):
        """
        初始化代理转发服务
        
        Args:
            target_base_url: 目标服务器基础 URL，默认使用配置中的值
        """
        self.target_base_url = target_base_url or settings.TARGET_BASE_URL
        self.client = ProxyClient(self.target_base_url, settings.REQUEST_TIMEOUT)
        
        # 重试配置
        self.max_retries = 3
        self.retry_delay = 1.0  # 重试延迟（秒）
        
        logger.info(f"Proxy forwarder initialized with target: {self.target_base_url}")
    
    async def forward_request(
        self,
        method: str,
        path: str,
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        stream: bool = False,
        api_key: Optional[str] = None
    ) -> Union[Response, StreamingResponse]:
        """
        转发 HTTP 请求到目标服务器
        
        Args:
            method: HTTP 方法
            path: 请求路径
            headers: 请求头
            data: 请求数据
            params: 查询参数
            stream: 是否为流式请求
            api_key: API 密钥（用于代理选择和重试）
            
        Returns:
            FastAPI 响应对象
        """
        try:
            logger.debug(f"Forwarding {method} request to {path}, stream={stream}")
            
            if stream:
                return await self._forward_stream_request(
                    method, path, headers, data, params, api_key
                )
            else:
                return await self._forward_normal_request(
                    method, path, headers, data, params, api_key
                )
                
        except Exception as e:
            logger.error(f"Request forwarding failed: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Request forwarding failed",
                    "detail": str(e),
                    "type": "proxy_error"
                }
            )
    
    async def _forward_normal_request(
        self,
        method: str,
        path: str,
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        api_key: Optional[str] = None
    ) -> Response:
        """转发普通请求"""
        last_exception = None
        
        for attempt in range(self.max_retries):
            try:
                response = await self.client.forward_request(
                    method=method,
                    path=path,
                    headers=headers,
                    data=data,
                    params=params,
                    api_key=api_key
                )
                
                # 准备响应头
                response_headers = self._prepare_response_headers(response.headers)
                
                # 获取响应内容
                content = response.content
                
                # 创建 FastAPI 响应
                return Response(
                    content=content,
                    status_code=response.status_code,
                    headers=response_headers,
                    media_type=response.headers.get("content-type", "application/json")
                )
                
            except Exception as e:
                last_exception = e
                attempt_num = attempt + 1
                
                logger.warning(f"Request attempt {attempt_num} failed: {str(e)}")
                
                if attempt_num < self.max_retries:
                    # 等待后重试
                    await asyncio.sleep(self.retry_delay * attempt_num)
                    logger.info(f"Retrying request (attempt {attempt_num + 1}/{self.max_retries})")
                else:
                    logger.error(f"All {self.max_retries} attempts failed")
        
        # 所有重试都失败了
        raise last_exception
    
    async def _forward_stream_request(
        self,
        method: str,
        path: str,
        headers: Dict[str, str] = None,
        data: Any = None,
        params: Dict[str, str] = None,
        api_key: Optional[str] = None
    ) -> StreamingResponse:
        """转发流式请求"""
        
        async def stream_generator():
            """流式响应生成器"""
            last_exception = None
            
            for attempt in range(self.max_retries):
                try:
                    async for chunk in self.client.forward_stream_request(
                        method=method,
                        path=path,
                        headers=headers,
                        data=data,
                        params=params,
                        api_key=api_key
                    ):
                        yield chunk
                    
                    # 成功完成流式传输
                    return
                    
                except Exception as e:
                    last_exception = e
                    attempt_num = attempt + 1
                    
                    logger.warning(f"Stream attempt {attempt_num} failed: {str(e)}")
                    
                    if attempt_num < self.max_retries:
                        # 等待后重试
                        await asyncio.sleep(self.retry_delay * attempt_num)
                        logger.info(f"Retrying stream request (attempt {attempt_num + 1}/{self.max_retries})")
                    else:
                        logger.error(f"All {self.max_retries} stream attempts failed")
                        # 发送错误信息，确保格式正确
                        error_detail = str(last_exception)
                        if "Content-Length" in error_detail:
                            error_detail = "Too little data for declared Content-Length"

                        error_data = {
                            "error": "Stream request failed",
                            "detail": error_detail,
                            "type": "proxy_stream_error"
                        }
                        yield f"data: {json.dumps(error_data)}\n\n".encode()
                        return
        
        return StreamingResponse(
            stream_generator(),
            media_type="text/event-stream",
            headers={
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "X-Accel-Buffering": "no",  # 禁用 Nginx 缓冲
                "Transfer-Encoding": "chunked"  # 明确指定分块传输
            }
        )
    
    def _prepare_response_headers(self, original_headers) -> Dict[str, str]:
        """
        准备响应头
        
        Args:
            original_headers: 原始响应头
            
        Returns:
            处理后的响应头
        """
        headers = {}
        
        # 需要排除的头部
        excluded_headers = {
            "content-length",  # FastAPI 会自动设置
            "connection",      # 连接相关头部
            "transfer-encoding",  # 传输编码
            "server",          # 服务器信息
            "date"             # 日期会自动更新
        }
        
        # 复制允许的头部
        for key, value in original_headers.items():
            if key.lower() not in excluded_headers:
                headers[key] = value
        
        # 添加代理标识
        headers["X-Proxy-Server"] = "LLM-Format-Proxy"
        
        return headers
    
    async def health_check(self) -> Dict[str, Any]:
        """
        健康检查
        
        Returns:
            健康状态信息
        """
        try:
            is_healthy = await self.client.health_check()
            
            return {
                "status": "healthy" if is_healthy else "unhealthy",
                "target_server": self.target_base_url,
                "timestamp": asyncio.get_event_loop().time(),
                "client_stats": self.client.get_stats()
            }
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            return {
                "status": "error",
                "error": str(e),
                "target_server": self.target_base_url,
                "timestamp": asyncio.get_event_loop().time()
            }
    
    async def forward_fastapi_request(self, request: Request, path: str) -> Union[Response, StreamingResponse]:
        """
        转发 FastAPI 请求对象
        
        Args:
            request: FastAPI 请求对象
            path: 目标路径
            
        Returns:
            FastAPI 响应对象
        """
        try:
            # 提取请求数据
            method = request.method
            headers = dict(request.headers)
            params = dict(request.query_params)
            
            # 获取请求体
            data = None
            if method in ["POST", "PUT", "PATCH"]:
                try:
                    body = await request.body()
                    if body:
                        content_type = headers.get("content-type", "")
                        if "application/json" in content_type:
                            data = json.loads(body.decode())
                        else:
                            data = body
                except Exception as e:
                    logger.warning(f"Failed to parse request body: {e}")
            
            # 检查是否为流式请求
            stream = (
                params.get("stream") == "true" or
                data and isinstance(data, dict) and data.get("stream") is True
            )
            
            # 提取 API 密钥（用于代理选择）
            api_key = self._extract_api_key(headers)
            
            # 转发请求
            return await self.forward_request(
                method=method,
                path=path,
                headers=headers,
                data=data,
                params=params,
                stream=stream,
                api_key=api_key
            )
            
        except Exception as e:
            logger.error(f"FastAPI request forwarding failed: {str(e)}")
            return JSONResponse(
                status_code=500,
                content={
                    "error": "Request processing failed",
                    "detail": str(e),
                    "type": "proxy_processing_error"
                }
            )
    
    def _extract_api_key(self, headers: Dict[str, str]) -> Optional[str]:
        """
        从请求头中提取 API 密钥
        
        Args:
            headers: 请求头字典
            
        Returns:
            API 密钥，如果没有找到则返回 None
        """
        # 检查常见的 API 密钥头部
        auth_headers = ["authorization", "x-api-key", "x-goog-api-key"]
        
        for header_name in auth_headers:
            header_value = headers.get(header_name) or headers.get(header_name.lower())
            if header_value:
                # 提取 Bearer token
                if header_value.startswith("Bearer "):
                    return header_value[7:]
                else:
                    return header_value
        
        return None
    
    def get_stats(self) -> Dict[str, Any]:
        """
        获取转发服务统计信息
        
        Returns:
            统计信息字典
        """
        return {
            "target_base_url": self.target_base_url,
            "max_retries": self.max_retries,
            "retry_delay": self.retry_delay,
            "client_stats": self.client.get_stats()
        }
