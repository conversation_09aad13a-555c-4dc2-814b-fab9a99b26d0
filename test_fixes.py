#!/usr/bin/env python3
"""
测试修复后的代理服务器功能
验证Content-Length问题、Gemini格式转换和流式响应是否已修复
"""

import json
import requests
import time
from typing import Dict, Any

def test_openai_format():
    """测试OpenAI格式（直接转发）"""
    print("1. 测试OpenAI格式...")
    try:
        url = "http://127.0.0.1:8002/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-key"
        }
        data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Hello from OpenAI format test"}
            ],
            "max_tokens": 100
        }
        
        print(f"URL: {url}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ OpenAI格式成功")
            return True
        else:
            print(f"✗ OpenAI格式失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ OpenAI格式异常: {e}")
        return False

def test_gemini_format():
    """测试Gemini格式（需要转换）"""
    print("\n2. 测试Gemini格式...")
    try:
        url = "http://127.0.0.1:8002/models/gemini-2.5-flash:generateContent"
        headers = {
            "Content-Type": "application/json"
        }
        params = {"key": "test-key"}
        data = {
            "contents": [
                {
                    "parts": [
                        {"text": "Hello from Gemini format test"}
                    ]
                }
            ]
        }
        
        print(f"URL: {url}")
        print(f"Params: {params}")
        print(f"Data: {json.dumps(data, indent=2)}")
        
        response = requests.post(url, headers=headers, json=data, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ Gemini格式成功")
            return True
        else:
            print(f"✗ Gemini格式失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Gemini格式异常: {e}")
        return False

def test_streaming():
    """测试流式请求"""
    print("\n3. 测试流式请求...")
    try:
        url = "http://127.0.0.1:8002/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-key"
        }
        data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Hello from streaming test"}
            ],
            "stream": True,
            "max_tokens": 50
        }
        
        print(f"URL: {url}")
        print("测试流式响应...")
        
        response = requests.post(url, headers=headers, json=data, stream=True, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ 流式请求成功")
            # 读取前几个数据块
            chunk_count = 0
            for line in response.iter_lines():
                if line:
                    print(f"流式数据: {line.decode()}")
                    chunk_count += 1
                    if chunk_count >= 3:  # 只读取前3个块
                        break
            return True
        else:
            print(f"✗ 流式请求失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 流式请求异常: {e}")
        return False

def test_health_check():
    """测试健康检查"""
    print("\n4. 测试健康检查...")
    try:
        url = "http://127.0.0.1:8002/health"
        
        response = requests.get(url, timeout=10)
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("✓ 健康检查成功")
            health_data = response.json()
            print(f"服务状态: {health_data.get('status', 'unknown')}")
            return True
        else:
            print(f"✗ 健康检查失败: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("测试修复后的LLM代理服务器")
    print("=" * 60)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    results = []
    
    # 执行测试
    results.append(("健康检查", test_health_check()))
    results.append(("OpenAI格式", test_openai_format()))
    results.append(("Gemini格式", test_gemini_format()))
    results.append(("流式请求", test_streaming()))
    
    # 输出结果
    print("\n" + "=" * 60)
    print("测试结果汇总")
    print("=" * 60)
    
    success_count = 0
    for test_name, success in results:
        status = "✓ 成功" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！修复成功！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
