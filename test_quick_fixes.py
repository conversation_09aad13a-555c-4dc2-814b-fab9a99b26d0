#!/usr/bin/env python3
"""
快速测试修复后的代理服务器功能
专注测试路径修复和Gemini格式转换
"""

import json
import requests
import time

def test_health():
    """测试健康检查"""
    print("1. 测试健康检查...")
    try:
        response = requests.get("http://127.0.0.1:8002/health", timeout=10)
        print(f"状态码: {response.status_code}")
        if response.status_code == 200:
            print("✓ 健康检查成功")
            return True
        else:
            print(f"✗ 健康检查失败: {response.text}")
            return False
    except Exception as e:
        print(f"✗ 健康检查异常: {e}")
        return False

def test_openai_path():
    """测试OpenAI路径是否正确"""
    print("\n2. 测试OpenAI路径...")
    try:
        url = "http://127.0.0.1:8002/v1/chat/completions"
        headers = {
            "Content-Type": "application/json",
            "Authorization": "Bearer test-key"
        }
        data = {
            "model": "gpt-3.5-turbo",
            "messages": [
                {"role": "user", "content": "Hello, test path fix"}
            ],
            "max_tokens": 50
        }
        
        response = requests.post(url, headers=headers, json=data, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ OpenAI路径测试成功")
            return True
        else:
            print(f"✗ OpenAI路径测试失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ OpenAI路径测试异常: {e}")
        return False

def test_gemini_conversion():
    """测试Gemini格式转换"""
    print("\n3. 测试Gemini格式转换...")
    try:
        url = "http://127.0.0.1:8002/models/gemini-2.5-flash:generateContent"
        headers = {
            "Content-Type": "application/json"
        }
        params = {"key": "test-key"}
        data = {
            "contents": [
                {
                    "parts": [
                        {"text": "Hello, test Gemini conversion"}
                    ]
                }
            ]
        }
        
        response = requests.post(url, headers=headers, json=data, params=params, timeout=30)
        print(f"状态码: {response.status_code}")
        print(f"响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            print("✓ Gemini转换测试成功")
            return True
        else:
            print(f"✗ Gemini转换测试失败")
            print(f"错误响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"✗ Gemini转换测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 50)
    print("快速测试修复后的功能")
    print("=" * 50)
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    results = []
    
    # 执行测试
    results.append(("健康检查", test_health()))
    results.append(("OpenAI路径", test_openai_path()))
    results.append(("Gemini转换", test_gemini_conversion()))
    
    # 输出结果
    print("\n" + "=" * 50)
    print("测试结果")
    print("=" * 50)
    
    success_count = 0
    for test_name, success in results:
        status = "✓ 成功" if success else "✗ 失败"
        print(f"{test_name}: {status}")
        if success:
            success_count += 1
    
    print(f"\n总计: {success_count}/{len(results)} 个测试通过")
    
    if success_count == len(results):
        print("🎉 所有测试都通过了！")
    else:
        print("⚠️  部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
